# 🚀 Cenovnik Aplikacija - Deployment Instrukcije

## 📦 Build Fajlovi

Aplikacija je uspešno build-ovana i spremna za upload. Imate dva načina za deployment:

### 1. **ZIP Arhiva (Preporučeno)**
- **Fajl:** `cenovnik-build.zip` (305 KB)
- **Sadržaj:** Kompletna static aplikacija spremna za upload

### 2. **Direktan Upload**
- **Folder:** `.output/public/`
- **Sadržaj:** Svi potrebni fajlovi za hosting

## 🌐 Hosting Opcije

### **Static Hosting Servisi:**
- ✅ **Netlify** - Drag & drop ZIP fajl
- ✅ **Vercel** - Drag & drop ili GitHub integration
- ✅ **GitHub Pages** - Upload sadržaj u repository
- ✅ **Firebase Hosting** - `firebase deploy`
- ✅ **Surge.sh** - `surge .output/public`
- ✅ **Cloudflare Pages** - Drag & drop ili Git integration

### **Tradicionalni Web Hosting:**
- ✅ **cPanel** - Upload ZIP i extract u public_html
- ✅ **FTP** - Upload sadržaj `.output/public/` foldera
- ✅ **Shared Hosting** - Bilo koji provider sa static file support

## 📋 Deployment Koraci

### **Netlify (Najlakši):**
1. Idite na [netlify.com](https://netlify.com)
2. Drag & drop `cenovnik-build.zip` na dashboard
3. Aplikacija je live za 30 sekundi!

### **Vercel:**
1. Idite na [vercel.com](https://vercel.com)
2. Kliknite "New Project"
3. Upload `cenovnik-build.zip`
4. Deploy!

### **cPanel/Shared Hosting:**
1. Ulogujte se u cPanel
2. Idite u File Manager
3. Upload `cenovnik-build.zip` u `public_html`
4. Extract arhivu
5. Aplikacija je dostupna na vašem domenu

### **FTP Upload:**
1. Konektujte se na FTP server
2. Upload sve fajlove iz `.output/public/` u root folder
3. Aplikacija je live!

## 🔧 Tehnički Detalji

### **Aplikacija Karakteristike:**
- **Tip:** Static Single Page Application (SPA)
- **Framework:** Nuxt 3 + Vue 3
- **Styling:** Bootstrap 5 + Custom CSS
- **Responsive:** Desktop + Mobile optimizovano
- **SEO:** Meta tagovi uključeni (`noindex, nofollow`)

### **Fajlovi u Build-u:**
```
├── index.html          # Glavna stranica
├── 200.html           # SPA fallback
├── 404.html           # Error stranica
├── cenovnik.json      # Podaci o kursevima
├── favicon.ico        # Ikona sajta
├── robots.txt         # SEO fajl
└── _nuxt/             # JavaScript i CSS fajlovi
    ├── *.js           # Aplikacija logika
    └── *.css          # Stilovi
```

### **Browser Podrška:**
- ✅ Chrome 90+
- ✅ Firefox 88+
- ✅ Safari 14+
- ✅ Edge 90+
- ✅ Mobile browsers

## 🎯 Funkcionalnosti

### **Desktop:**
- Sidebar sa filterima
- Sortiranje tabele
- Paginacija (10, 25, 50, 100 itema)
- Responsive design

### **Mobilni:**
- Slide-out filter panel
- Touch-friendly interface
- Sticky scroll indikator
- Optimizovana paginacija (10, 25, 50 itema)

### **Filtri:**
- 🔍 Pretraga po nazivu
- 🏢 Oblast rada
- 📚 Vrsta kursa
- 🎓 Tip obuke
- 📊 Nivo kursa
- ⏱️ Trajanje kursa
- 💰 Cena kursa

### **Sortiranje:**
- 📝 Naziv (A-Z, Z-A)
- ⏱️ Trajanje (rastući/opadajući)
- 💰 Cena (rastući/opadajući)

## 🚨 Važne Napomene

1. **JSON Fajl:** `cenovnik.json` mora biti u root folderu
2. **Relativni Putevi:** Svi linkovi su relativni - rade na bilo kom domenu
3. **HTTPS:** Preporučuje se HTTPS za production
4. **Cache:** Browser će cache-ovati fajlove za bolje performanse

## 🔄 Ažuriranje Podataka

Za ažuriranje cenovnika:
1. Editujte `public/cenovnik.json` fajl
2. Pokrenite `npm run generate`
3. Upload novi build

## 📞 Podrška

Aplikacija je potpuno funkcionalna i testirana. Sve funkcionalnosti rade kako treba:
- ✅ Filtri i pretraga
- ✅ Sortiranje
- ✅ Paginacija
- ✅ Responsive design
- ✅ Mobile optimizacija

**Build je spreman za production upload!** 🎉
