# Cenovnik Akademija Oxford - Nuxt 3 Aplikacija

Moderna web aplikacija za prikaz cenovnika kurseva Akademije Oxford po gradovima, izgrađena sa Nuxt 3 i Bootstrap 5.

## Funkcionalnosti

- **Početna stranica** sa linkovima za sve gradove
- **Dinamičke rute** za svaki grad (`/cenovnik/[grad]`)
- **Responzivni dizajn** optimizovan za desktop, tablet i mobilne uređaje
- **Napredni filtri** za pretragu kurseva
- **Sortiranje** po različitim kriterijumima
- **Paginacija** sa različitim opcijama za desktop i mobilne uređaje
- **SEO optimizacija** sa meta tagovima za svaki grad
- **Bootstrap 5** stilovi umesto starih Bootstrap 3 klasa

## Struktura aplikacije

```
/
├── pages/
│   ├── index.vue                 # Početna stranica sa gradovima
│   └── cenovnik/
│       └── [grad].vue           # Dinamička ruta za gradove
├── assets/css/
│   └── main.css                 # Glavni CSS fajl sa Bootstrap 5 stilovima
├── public/
│   └── cenovnik.json           # JSON podaci sa kursevima
└── nuxt.config.ts              # Nuxt konfiguracija
```

## Podržani gradovi

Aplikacija podržava sledeće gradove sa odgovarajućim slug-ovima:

- Aranđelovac (`/cenovnik/arandjelovac`)
- Banovo Brdo (`/cenovnik/banovo-brdo`)
- Beograd (`/cenovnik/beograd`)
- Bor (`/cenovnik/bor`)
- Borča (`/cenovnik/borca`)
- Čačak (`/cenovnik/cacak`)
- Ćuprija (`/cenovnik/cuprija`)
- Inđija (`/cenovnik/indjija`)
- Jagodina (`/cenovnik/jagodina`)
- Kragujevac (`/cenovnik/kragujevac`)
- Kraljevo (`/cenovnik/kraljevo`)
- Kruševac (`/cenovnik/krusevac`)
- Leskovac (`/cenovnik/leskovac`)
- Mladenovac (`/cenovnik/mladenovac`)
- Niš (`/cenovnik/nis`)
- Novi Beograd (`/cenovnik/novi-beograd`)
- Novi Pazar (`/cenovnik/novi-pazar`)
- Novi Sad (`/cenovnik/novi-sad`)
- Obrenovac (`/cenovnik/obrenovac`)
- Pančevo (`/cenovnik/pancevo`)
- Paraćin (`/cenovnik/paracin`)
- Požarevac (`/cenovnik/pozarevac`)
- Smederevo (`/cenovnik/smederevo`)
- Sombor (`/cenovnik/sombor`)
- Subotica (`/cenovnik/subotica`)
- Sr. Mitrovica (`/cenovnik/sremska-mitrovica`)
- Šabac (`/cenovnik/sabac`)
- Užice (`/cenovnik/uzice`)
- Valjevo (`/cenovnik/valjevo`)
- Voždovac (`/cenovnik/vozdovac`)
- Vranje (`/cenovnik/vranje`)
- Vršac (`/cenovnik/vrsac`)
- Zaječar (`/cenovnik/zajecar`)
- Zemun (`/cenovnik/zemun`)
- Zrenjanin (`/cenovnik/zrenjanin`)
- Ruma (`/cenovnik/ruma`)

## Setup

Instalirajte dependencies:

```bash
npm install
```

## Development Server

Pokrenite development server na `http://localhost:3000/cenovnik/`:

```bash
npm run dev
```

## Production

Izgradite aplikaciju za produkciju:

```bash
npm run build
```

Pregled production build-a:

```bash
npm run preview
```

## Deployment

Aplikacija je SPA (Single Page Application) koja generiše statičke fajlove. Za deployment:

1. Izgradite aplikaciju: `npm run build`
2. Kopirajte sadržaj `.output/public/` foldera na server
3. Konfigurišite server da služi statičke fajlove i redirectuje sve rute na `index.html`

### Apache (.htaccess)

```apache
RewriteEngine On
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule . /index.html [L]
```

### Nginx

```nginx
location / {
    try_files $uri $uri/ /index.html;
}
```

## Tehnologije

- **Nuxt 3** - Vue.js framework
- **Bootstrap 5** - CSS framework
- **Bootstrap Icons** - Ikonice
- **TypeScript** - Type safety
- **Vite** - Build tool

## Migracija sa Bootstrap 3 na Bootstrap 5

Aplikacija je ažurirana sa Bootstrap 3 na Bootstrap 5 klase:

- `col-xs-*` → `col-*`
- `hidden-*` → `d-none d-*-block`
- `visible-*` → `d-*-block`
- `btn-default` → `btn-secondary`
- `panel` → `card`
- `panel-body` → `card-body`
- `form-group` → `mb-3`
- `input-group-addon` → `input-group-text`

## Kontakt

Za pitanja i podršku kontaktirajte Akademiju Oxford.
