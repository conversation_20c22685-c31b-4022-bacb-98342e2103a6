<template>
  <div>
    <!-- Navbar -->
    <AppNavbar />

    <!-- Main Content -->
    <div class="min-vh-100 bg-light py-4 mt-5">
      <div class="container-fluid">
        <div class="text-center my-4">
          <h1 class="display-5 fw-bold text-orange mb-3">Cenovnik {{ gradNaziv }}</h1>
          <nav aria-label="breadcrumb">
            <ol class="breadcrumb justify-content-center">
              <li class="breadcrumb-item">
                <a href="https://www.akademijaoxford.com" class="text-orange text-decoration-none">
                  <i class="bi bi-house-fill me-1"></i>Početna
                </a>
              </li>
              <li class="breadcrumb-item">
                <NuxtLink to="/" class="text-orange text-decoration-none">Cenovnik</NuxtLink>
              </li>
              <li class="breadcrumb-item active" aria-current="page">{{ gradNaziv }}</li>
            </ol>
          </nav>
        </div>

      <div class="row g-4">
        <!-- Sidebar sa filterima -->
        <div class="col-lg-3 col-md-4 d-none d-md-block">
          <div class="sidebar bg-white rounded shadow p-4 sticky-top">
            <div class="d-flex align-items-center justify-content-between mb-3">
              <h5 class="text-orange mb-0">
                <i class="bi bi-funnel me-2"></i>Filteri
              </h5>
              <button
                @click="clearAllFilters"
                class="btn btn-outline-secondary btn-sm"
                :disabled="!hasActiveFilters"
              >
                <i class="bi bi-x-circle me-1"></i>Obriši sve
              </button>
            </div>

            <!-- Oblast/Struka -->
            <div class="filter-group mb-4">
              <label class="form-label fw-semibold">
                <i class="bi bi-building me-2"></i>Oblast/Struka
              </label>
              <select v-model="filters.oblast" class="form-select">
                <option :value="null">Sve oblasti</option>
                <option v-for="oblast in oblasti" :key="oblast" :value="oblast">
                  {{ oblast }}
                </option>
              </select>
            </div>

            <!-- Vrsta kursa -->
            <div class="filter-group mb-4">
              <label class="form-label fw-semibold">
                <i class="bi bi-people me-2"></i>Vrsta kursa
              </label>
              <div class="form-check-group">
                <div class="form-check" v-for="vrsta in vrste" :key="vrsta">
                  <input
                    class="form-check-input"
                    type="checkbox"
                    :id="'vrsta-' + vrsta"
                    :value="vrsta"
                    v-model="filters.vrsta"
                  >
                  <label class="form-check-label" :for="'vrsta-' + vrsta">
                    {{ vrsta }}
                  </label>
                </div>
              </div>
            </div>

            <!-- Tip obuke -->
            <div class="filter-group mb-4">
              <label class="form-label fw-semibold">
                <i class="bi bi-mortarboard me-2"></i>Tip obuke
              </label>
              <div class="d-flex flex-md-column flex-lg-row gap-2 gap-lg-0" role="group">
                <button
                  type="button"
                  class="btn me-2"
                  :class="filters.tipObuke === null ? 'btn-primary' : 'btn-outline-primary'"
                  @click="filters.tipObuke = null"
                >
                  Svi tipovi
                </button>
                <template v-for="tip in tipoviObuke" :key="tip">
                  <button
                    type="button"
                    class="btn me-2"
                    :class="filters.tipObuke === tip ? 'btn-primary' : 'btn-outline-primary'"
                    @click="filters.tipObuke = tip"
                  >
                    {{ tip }}
                  </button>
                </template>
              </div>
            </div>

            <!-- Nivo kursa -->
            <div class="filter-group mb-4">
              <label class="form-label fw-semibold">
                <i class="bi bi-bar-chart-steps me-2"></i>Nivo kursa
              </label>
              <select v-model="filters.nivo" class="form-select">
                <option :value="null">Svi nivoi</option>
                <option v-for="nivo in nivoi" :key="nivo" :value="nivo">
                  {{ nivo }}
                </option>
              </select>
            </div>

            <!-- Trajanje kursa -->
            <div class="filter-group mb-4">
              <label class="form-label fw-semibold">
                <i class="bi bi-clock me-2"></i>Trajanje (časova)
              </label>
              <div class="row g-2">
                <div class="col-6">
                  <input
                    v-model.number="filters.trajanjeOd"
                    type="number"
                    class="form-control form-control-sm"
                    placeholder="Od"
                    min="0"
                  >
                </div>
                <div class="col-6">
                  <input
                    v-model.number="filters.trajanjeDo"
                    type="number"
                    class="form-control form-control-sm"
                    placeholder="Do"
                    min="0"
                  >
                </div>
              </div>
              <small class="text-muted">{{ trajanjeRange }}</small>
            </div>

            <!-- Cena kursa -->
            <div class="filter-group mb-4">
              <label class="form-label fw-semibold">
                <i class="bi bi-currency-exchange me-2"></i>Cena (RSD)
              </label>
              <div class="row g-2">
                <div class="col-6">
                  <input
                    v-model.number="filters.cenaOd"
                    type="number"
                    class="form-control form-control-sm"
                    placeholder="Od"
                    min="0"
                    step="1000"
                  >
                </div>
                <div class="col-6">
                  <input
                    v-model.number="filters.cenaDo"
                    type="number"
                    class="form-control form-control-sm"
                    placeholder="Do"
                    min="0"
                    step="1000"
                  >
                </div>
              </div>
              <small class="text-muted">{{ cenaRange }}</small>
            </div>

            <!-- Rezultati -->
            <div class="filter-results bg-light rounded p-3">
              <div class="d-flex align-items-center justify-content-between">
                <span class="fw-semibold text-orange">Rezultati:</span>
                <span class="badge bg-orange">{{ filteredData.length }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Glavna oblast sa rezultatima -->
        <div class="col-lg-9 col-md-8 col-12">
          <div class="results-area bg-white rounded shadow p-4">
            <!-- Mobilni header sa filterima i sortiranjem -->
            <div class="d-block d-md-none mb-3">
              <div class="row g-2 mb-2 mobile-filter-sort-row">
                <div class="col-12">
                  <!-- Pretraga -->
                  <div class="filter-group mb-4">
                    <label class="form-label fw-semibold text-uppercase h5">
                      <i class="bi bi-search me-2"></i>Pretraga po nazivu
                    </label>
                    <input
                      v-model="filters.search"
                      type="text"
                      placeholder="Unesite naziv kursa..."
                      class="form-control"
                    >
                  </div>
                </div>
                <div class="col-6">
                  <button
                    @click="toggleMobileFilters"
                    class="btn btn-outline-primary w-100 d-flex align-items-center justify-content-center"
                  >
                    <i class="bi bi-funnel me-2"></i>
                    Filteri
                    <span v-if="hasActiveFilters" class="badge bg-orange ms-2">{{ activeFiltersCount }}</span>
                  </button>
                </div>
                <div class="col-6">
                  <select v-model="sortBy" class="form-select form-select-outline-primary mb-3">
                    <option value="naziv">Naziv A-Z ▲</option>
                    <option value="naziv-desc">Naziv Z-A ▼</option>
                    <option value="trajanje">Trajanje (rastući) ▲</option>
                    <option value="trajanje-desc">Trajanje (opadajući) ▼</option>
                    <option value="cena">Cena (rastući) ▲</option>
                    <option value="cena-desc">Cena (opadajući) ▼</option>
                  </select>
                </div>
              </div>
              <!-- Mobilni info i kontrole u dva reda -->
              <!-- <div class="row g-2">
                <div class="col-12">
                  <span class="text-muted small">Prikazano {{ paginatedData.length }} od {{ filteredData.length }} kurseva</span>
                </div>
                <div class="col-12">
                  <div class="d-flex align-items-center gap-2 text-nowrap mobile-pagination-row">
                    <span class="text-muted small">Prikaži</span>
                    <select v-model="selectedMobileItemsPerPage" class="form-select form-select-sm mobile-pagination-select" style="min-width: 60px; width: auto; font-size: 0.75rem; padding: 0.25rem 1.5rem 0.25rem 0.5rem;">
                      <option v-for="option in mobileItemsPerPageOptions" :key="option" :value="option">
                        {{ option }}
                      </option>
                    </select>
                    <span class="text-muted small">po stranici</span>
                  </div>
                </div>
              </div> -->
            </div>

            <!-- Desktop sortiranje i prikaz -->
            <div class="d-none d-md-flex justify-content-between align-items-center mb-3 flex-wrap">
              <div class="d-flex align-items-center mb-2 mb-md-0 gap-3">
                <!-- <span class="text-muted">
                  Prikazano {{ paginatedData.length }} od {{ filteredData.length }} kurseva
                </span>
                <div class="d-flex align-items-center gap-2">
                  <span class="text-muted small">Prikaži:</span>
                  <select v-model="selectedDesktopItemsPerPage" class="form-select form-select-sm" style="width: auto;">
                    <option v-for="option in desktopItemsPerPageOptions" :key="option" :value="option">
                      {{ option }}
                    </option>
                  </select>
                  <span class="text-muted small">po stranici</span>
                </div> -->
                <div class="filter-group mb-4">
                  <label class="form-label fw-semibold text-uppercase h5">
                    <i class="bi bi-search me-2"></i>Pretraga po nazivu
                  </label>
                  <input
                    v-model="filters.search"
                    type="text"
                    placeholder="Unesite naziv kursa..."
                    class="form-control search-input"
                  >
                </div>
              </div>
              <div class="d-flex align-items-center gap-2 sort-controls">
                <label class="form-label mb-0 me-2 text-nowrap">Sortiranje:</label>
                <select v-model="sortBy" class="form-select form-select-sm" style="min-width: 220px;">
                  <option value="naziv">Naziv A-Z ▲</option>
                  <option value="naziv-desc">Naziv Z-A ▼</option>
                  <option value="trajanje">Trajanje (rastući) ▲</option>
                  <option value="trajanje-desc">Trajanje (opadajući) ▼</option>
                  <option value="cena">Cena (rastući) ▲</option>
                  <option value="cena-desc">Cena (opadajući) ▼</option>
                </select>
              </div>
            </div>

            <!-- Status poruke -->
            <div v-if="error" class="alert alert-danger">
              <i class="bi bi-exclamation-triangle-fill me-2"></i>
              Greška pri učitavanju podataka. Molimo pokušajte ponovo.
            </div>

            <div v-else-if="isLoading" class="alert alert-info">
              <div class="d-flex align-items-center">
                <div class="spinner-border spinner-border-sm me-2" role="status">
                  <span class="visually-hidden">Učitavanje...</span>
                </div>
                Učitavanje podataka...
              </div>
            </div>

            <div v-else-if="!kursevi || kursevi.length === 0" class="alert alert-warning">
              <i class="bi bi-info-circle-fill me-2"></i>
              Nema dostupnih podataka.
            </div>

            <!-- Responzivna tabela -->
            <div v-else-if="filteredData.length > 0" class="table-responsive mb-4">
              <table class="table table-striped table-hover">
                <thead>
                  <tr>
                    <th>Naziv</th>
                    <th>Trajanje (časova)</th>
                    <th>Tip kursa</th>
                    <th>Vrsta kursa</th>
                    <th>Cena (RSD)</th>
                  </tr>
                </thead>
                <tbody>
                  <tr
                    v-for="item in paginatedData"
                    :key="item.Kurs_Red_Br"
                  >
                    <td>
                      <div class="d-flex align-items-center">
                        <i :class="getVrstaIcon(item.Vrsta)" class="me-2 text-orange course-type-icon"></i>
                        <span>{{ item.Kurs_Naziv }}</span>
                      </div>
                    </td>
                    <td>{{ item.Kurs_Trajanje_Kursa }}</td>
                    <td>{{ item.Kurs_Obuka }}</td>
                    <td>{{ item.Vrsta }}</td>
                    <td class="fw-medium">{{ formatPrice(item.Kcpp_Cena) }}</td>
                  </tr>
                </tbody>
              </table>
            </div>

            <!-- Poruka kada nema rezultata -->
            <div v-else class="alert alert-info text-center">
              <i class="bi bi-info-circle me-2"></i>
              Nema rezultata za zadate filtere. Pokušajte sa drugačijim kriterijumima pretrage.
            </div>
            
            <div class="d-block d-md-none">
              <p class="badge bg-orange text-light">⟷ Prevucite za više kolona</p>
            </div>

            <!-- Paginacija -->
            <div v-if="filteredData && filteredData.length > 0" class="mt-4">
              <!-- Desktop paginacija -->
              <div class="d-none d-md-flex justify-content-center align-items-center gap-2">
                <button
                  @click="prevPage"
                  :disabled="currentPage === 1"
                  class="btn btn-primary"
                >
                  <i class="bi bi-chevron-left"></i> Prethodna
                </button>
                <span class="mx-2">Strana {{ currentPage }} od {{ totalPages }}</span>
                <button
                  @click="nextPage"
                  :disabled="currentPage === totalPages"
                  class="btn btn-primary"
                >
                  Sledeća <i class="bi bi-chevron-right"></i>
                </button>
              </div>

              <!-- Mobilna paginacija u jednom redu -->
              <div class="d-md-none d-flex gap-2">
                <button
                  @click="prevPage"
                  :disabled="currentPage === 1"
                  class="btn btn-primary btn-sm"
                >
                  <i class="bi bi-chevron-left"></i> Prethodna
                </button>
                <span class="text-muted small">Strana {{ currentPage }} od {{ totalPages }}</span>
                <button
                  @click="nextPage"
                  :disabled="currentPage === totalPages"
                  class="btn btn-primary btn-sm"
                >
                  Sledeća <i class="bi bi-chevron-right"></i>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    </div>

    <!-- Mobilni slide-out filter panel -->
    <div
      class="mobile-filter-overlay d-md-none"
      :class="{ 'show': showMobileFilters }"
      @click="closeMobileFilters"
    ></div>

    <div
      class="mobile-filter-panel d-md-none"
      :class="{ 'show': showMobileFilters }"
    >
      <div class="mobile-filter-header">
        <h5 class="text-orange mb-0">
          <i class="bi bi-funnel me-2"></i>Filteri
        </h5>
        <button
          @click="closeMobileFilters"
          class="btn btn-sm btn-outline-secondary"
        >
          <i class="bi bi-x-lg"></i>
        </button>
      </div>

      <div class="mobile-filter-content">

        <!-- Oblast/Struka -->
        <div class="filter-group mb-4">
          <label class="form-label fw-semibold">
            <i class="bi bi-building me-2"></i>Oblast/Struka
          </label>
          <select v-model="filters.oblast" class="form-select">
            <option :value="null">Sve oblasti</option>
            <option v-for="oblast in oblasti" :key="oblast" :value="oblast">
              {{ oblast }}
            </option>
          </select>
        </div>

        <!-- Vrsta kursa -->
        <div class="filter-group mb-4">
          <label class="form-label fw-semibold">
            <i class="bi bi-people me-2"></i>Vrsta kursa
          </label>
          <div class="form-check-group">
            <div class="form-check" v-for="vrsta in vrste" :key="vrsta">
              <input
                class="form-check-input"
                type="checkbox"
                :id="'mobile-vrsta-' + vrsta"
                :value="vrsta"
                v-model="filters.vrsta"
              >
              <label class="form-check-label" :for="'mobile-vrsta-' + vrsta">
                {{ vrsta }}
              </label>
            </div>
          </div>
        </div>

        <!-- Tip obuke -->
        <div class="filter-group mb-4">
          <label class="form-label fw-semibold">
            <i class="bi bi-mortarboard me-2"></i>Tip obuke
          </label>
          <div class="d-grid gap-2">
            <button
              type="button"
              class="btn w-50"
              :class="filters.tipObuke === null ? 'btn-primary' : 'btn-outline-primary'"
              @click="filters.tipObuke = null"
            >
              Svi tipovi
            </button>
            <template v-for="tip in tipoviObuke" :key="tip">
              <button
                type="button"
                class="btn w-50"
                :class="filters.tipObuke === tip ? 'btn-primary' : 'btn-outline-primary'"
                @click="filters.tipObuke = tip"
              >
                {{ tip }}
              </button>
            </template>
          </div>
        </div>

        <!-- Nivo kursa -->
        <div class="filter-group mb-4">
          <label class="form-label fw-semibold">
            <i class="bi bi-bar-chart-steps me-2"></i>Nivo kursa
          </label>
          <select v-model="filters.nivo" class="form-select">
            <option :value="null">Svi nivoi</option>
            <option v-for="nivo in nivoi" :key="nivo" :value="nivo">
              {{ nivo }}
            </option>
          </select>
        </div>

        <!-- Trajanje kursa -->
        <div class="filter-group mb-4">
          <label class="form-label fw-semibold">
            <i class="bi bi-clock me-2"></i>Trajanje (časova)
          </label>
          <div class="row g-2">
            <div class="col-6">
              <input
                v-model.number="filters.trajanjeOd"
                type="number"
                class="form-control form-control-sm"
                placeholder="Od"
                min="0"
              >
            </div>
            <div class="col-6">
              <input
                v-model.number="filters.trajanjeDo"
                type="number"
                class="form-control form-control-sm"
                placeholder="Do"
                min="0"
              >
            </div>
          </div>
          <small class="text-muted">{{ trajanjeRange }}</small>
        </div>

        <!-- Cena kursa -->
        <div class="filter-group mb-4">
          <label class="form-label fw-semibold">
            <i class="bi bi-currency-exchange me-2"></i>Cena (RSD)
          </label>
          <div class="row g-2">
            <div class="col-6">
              <input
                v-model.number="filters.cenaOd"
                type="number"
                class="form-control form-control-sm"
                placeholder="Od"
                min="0"
                step="1000"
              >
            </div>
            <div class="col-6">
              <input
                v-model.number="filters.cenaDo"
                type="number"
                class="form-control form-control-sm"
                placeholder="Do"
                min="0"
                step="1000"
              >
            </div>
          </div>
          <small class="text-muted">{{ cenaRange }}</small>
        </div>
      </div>

      <div class="mobile-filter-footer">
        <div class="row g-2">
          <div class="col-6">
            <button
              @click="clearAllFilters"
              class="btn btn-outline-secondary w-100"
              :disabled="!hasActiveFilters"
            >
              <i class="bi bi-x-circle me-1"></i>Obriši sve
            </button>
          </div>
          <div class="col-6">
            <button
              @click="closeMobileFilters"
              class="btn btn-primary w-100"
            >
              Primeni ({{ filteredData.length }})
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Footer -->
    <AppFooter />
  </div>
</template>

<script setup>
// Dobijanje parametra iz rute
const route = useRoute()
const gradSlug = route.params.grad

// Mapiranje slug-ova u nazive gradova
const gradoviMapping = {
  'arandjelovac': 'Aranđelovac',
  'banovo-brdo': 'Banovo Brdo',
  'beograd': 'Beograd',
  'bor': 'Bor',
  'borca': 'Borča',
  'cacak': 'Čačak',
  'cuprija': 'Ćuprija',
  'indjija': 'Inđija',
  'jagodina': 'Jagodina',
  'kikinda': 'Kikinda',
  'kragujevac': 'Kragujevac',
  'kraljevo': 'Kraljevo',
  'krusevac': 'Kruševac',
  'leskovac': 'Leskovac',
  'mladenovac': 'Mladenovac',
  'nis': 'Niš',
  'novi-beograd': 'Novi Beograd',
  'novi-pazar': 'Novi Pazar',
  'novi-sad': 'Novi Sad',
  'obrenovac': 'Obrenovac',
  'pancevo': 'Pančevo',
  'pirot': 'Pirot',
  'paracin': 'Paraćin',
  'pozarevac': 'Požarevac',
  'ruma': 'Ruma',
  'smederevo': 'Smederevo',
  'sombor': 'Sombor',
  'subotica': 'Subotica',
  'sremska-mitrovica': 'Sr. Mitrovica',
  'stara-pazova': 'Stara Pazova',
  'sabac': 'Šabac',
  'uzice': 'Užice',
  'valjevo': 'Valjevo',
  'vozdovac': 'Voždovac',
  'vranje': 'Vranje',
  'vrsac': 'Vršac',
  'zajecar': 'Zaječar',
  'zemun': 'Zemun',
  'zrenjanin': 'Zrenjanin',
  'zvezdara': 'Zvezdara'
}

// Dobijanje naziva grada
const gradNaziv = computed(() => {
  return gradoviMapping[gradSlug] || 'Nepoznat grad'
})

// Učitavanje meta podataka za gradove
const gradMeta = ref(null)

// SEO meta tags sa jedinstvenim podacima za svaki grad
useSeoMeta({
  title: computed(() => {
    if (gradMeta.value && gradMeta.value[gradSlug]) {
      return gradMeta.value[gradSlug].title
    }
    return `Cenovnik kurseva - ${gradNaziv.value}, cene`
  }),
  description: computed(() => {
    if (gradMeta.value && gradMeta.value[gradSlug]) {
      return gradMeta.value[gradSlug].description
    }
    return `Akademija Oxford - Cenovnik za sve kurseve i obuke u gradu ${gradNaziv.value}`
  }),
  keywords: computed(() => {
    if (gradMeta.value && gradMeta.value[gradSlug]) {
      return gradMeta.value[gradSlug].keywords
    }
    return `cenovnik akademija oxford ${gradNaziv.value.toLowerCase()}, cene kurseva ${gradNaziv.value.toLowerCase()}, cene obuka ${gradNaziv.value.toLowerCase()}`
  }),
  ogTitle: computed(() => {
    if (gradMeta.value && gradMeta.value[gradSlug]) {
      return gradMeta.value[gradSlug].title
    }
    return `Cenovnik kurseva - ${gradNaziv.value}, cene`
  }),
  ogDescription: computed(() => {
    if (gradMeta.value && gradMeta.value[gradSlug]) {
      return gradMeta.value[gradSlug].ogDescription
    }
    return `Akademija Oxford - Cenovnik za sve kurseve i obuke u gradu ${gradNaziv.value}`
  }),
  ogUrl: computed(() => `https://www.akademijaoxford.com/cenovnik/${gradSlug}`)
})

// Proverava da li je grad valjan
if (!gradoviMapping[gradSlug]) {
  throw createError({
    statusCode: 404,
    statusMessage: 'Grad nije pronađen'
  })
}

const kursevi = ref(null)
const error = ref(null)
const isLoading = ref(true)

// Screen size detection
const isMobile = ref(false)

function updateScreenSize() {
  isMobile.value = window.innerWidth < 768
}

// Bootstrap inicijalizacija
const { initializeAllComponents } = useBootstrap()

// Učitavanje podataka
onMounted(async () => {
  try {
    // Učitaj meta podatke za gradove
    try {
      const metaResponse = await fetch('./meta/gradovi-meta.json')
      if (metaResponse.ok) {
        gradMeta.value = await metaResponse.json()
        console.log('✅ Uspešno učitani meta podaci za gradove')
      }
    } catch (metaError) {
      console.log('⚠️ Greška pri učitavanju meta podataka:', metaError.message)
    }

    let data = null
    let dataSource = ''

    // Prvo pokušaj da učitaš specifični cenovnik za grad
    const gradSpecificUrl = `./cenovnik/cenovnik-${gradSlug}.json`

    try {
      console.log(`Pokušavam da učitam specifični cenovnik: ${gradSpecificUrl}`)
      const gradResponse = await fetch(gradSpecificUrl)

      if (gradResponse.ok) {
        data = await gradResponse.json()
        dataSource = `specifični cenovnik za ${gradNaziv.value}`
        console.log(`✅ Uspešno učitan ${dataSource}:`, data.length, 'kurseva')
      } else {
        throw new Error(`Specifični cenovnik nije pronađen (${gradResponse.status})`)
      }
    } catch (gradError) {
      console.log(`⚠️ ${gradError.message}`)
      console.log('Pokušavam da učitam opšti cenovnik: ./cenovnik/response.json')

      // Ako specifični cenovnik ne postoji, učitaj opšti
      const generalResponse = await fetch('./cenovnik/response.json')
      if (!generalResponse.ok) {
        throw new Error(`HTTP error! status: ${generalResponse.status}`)
      }
      data = await generalResponse.json()
      dataSource = 'opšti cenovnik'
      console.log(`✅ Uspešno učitan ${dataSource}:`, data.length, 'kurseva')
    }

    kursevi.value = data
    console.log(`📊 Ukupno učitano kurseva iz ${dataSource}:`, data.length)

    // Setup screen size detection
    updateScreenSize()
    window.addEventListener('resize', updateScreenSize)

    // Inicijalizuj Bootstrap komponente
    initializeAllComponents()
  } catch (e) {
    console.error('Error loading data:', e)
    error.value = e
  } finally {
    isLoading.value = false
  }
})

// Filtri
const filters = ref({
  search: '',
  oblast: null,
  vrsta: [],
  tipObuke: null,
  nivo: null,
  trajanjeOd: null,
  trajanjeDo: null,
  cenaOd: null,
  cenaDo: null
})

const sortBy = ref('naziv')
const currentPage = ref(1)

// Items per page - različite opcije za desktop i mobilni
const desktopItemsPerPageOptions = [10, 25, 50, 100]
const mobileItemsPerPageOptions = [10, 25, 50]
const selectedDesktopItemsPerPage = ref(25)
const selectedMobileItemsPerPage = ref(10)

// Computed items per page na osnovu screen size
const itemsPerPage = computed(() => {
  return isMobile.value ? selectedMobileItemsPerPage.value : selectedDesktopItemsPerPage.value
})

// Mobilni filtri
const showMobileFilters = ref(false)

// Computed properties za opcije filtera
const oblasti = computed(() => {
  if (!kursevi.value) return []
  const set = new Set()
  kursevi.value.forEach(d => d.Oblast && set.add(d.Oblast))
  return Array.from(set).sort()
})

const vrste = computed(() => {
  if (!kursevi.value) return []
  const set = new Set()
  kursevi.value.forEach(d => d.Vrsta && set.add(d.Vrsta))
  return Array.from(set).sort()
})

const tipoviObuke = computed(() => {
  if (!kursevi.value) return []
  const set = new Set()
  kursevi.value.forEach(d => d.Kurs_Obuka && set.add(d.Kurs_Obuka))
  return Array.from(set).sort()
})

// Dinamičko filtriranje nivoa na osnovu izabrane oblasti/struka
const nivoi = computed(() => {
  if (!kursevi.value) return []

  let filteredKursevi = kursevi.value

  // Ako je izabrana oblast, filtriraj kurseve po toj oblasti
  if (filters.value.oblast) {
    filteredKursevi = kursevi.value.filter(d => d.Oblast === filters.value.oblast)
  }

  const set = new Set()
  filteredKursevi.forEach(d => d.Nivo && set.add(d.Nivo))
  // Mapiramo skraćenice u pune nazive za prikaz
  return Array.from(set).map(nivo => mapNivoToFullName(nivo)).sort()
})

// Opsezi za prikaz
const trajanjeRange = computed(() => {
  if (!kursevi.value || kursevi.value.length === 0) return ''
  const trajanja = kursevi.value.map(k => k.Kurs_Trajanje_Kursa).filter(t => t && t > 0)
  if (trajanja.length === 0) return ''
  const min = Math.min(...trajanja)
  const max = Math.max(...trajanja)
  return `Dostupno: ${min} - ${max} časova`
})

const cenaRange = computed(() => {
  if (!kursevi.value || kursevi.value.length === 0) return ''
  const cene = kursevi.value.map(k => k.Kcpp_Cena).filter(c => c && c > 0)
  if (cene.length === 0) return ''
  const min = Math.min(...cene)
  const max = Math.max(...cene)
  return `Dostupno: ${formatPrice(min)} - ${formatPrice(max)}`
})

// Provera da li ima aktivnih filtera
const hasActiveFilters = computed(() => {
  return filters.value.search ||
         filters.value.oblast ||
         filters.value.vrsta.length > 0 ||
         filters.value.tipObuke ||
         filters.value.nivo ||
         filters.value.trajanjeOd ||
         filters.value.trajanjeDo ||
         filters.value.cenaOd ||
         filters.value.cenaDo
})

// Broj aktivnih filtera
const activeFiltersCount = computed(() => {
  let count = 0
  if (filters.value.search) count++
  if (filters.value.oblast) count++
  if (filters.value.vrsta.length > 0) count++
  if (filters.value.tipObuke) count++
  if (filters.value.nivo) count++
  if (filters.value.trajanjeOd || filters.value.trajanjeDo) count++
  if (filters.value.cenaOd || filters.value.cenaDo) count++
  return count
})

  // Helper funkcija za skidanje dijakritika
function stripDiacritics(str) {
  // Normalize u NFD oblik pa ukloni sve dijakritičke znakove
  return str.normalize('NFD').replace(/[\u0300-\u036f]/g, '');
}

const filteredData = computed(() => {
  if (!kursevi.value) return []

  let result = kursevi.value

  if (filters.value.search) {
    // 1. Uzmi korisnički unos
    const raw = filters.value.search.trim()
    // 2. Skini dijakritike i spusti u mala slova
    const noDiacritics = stripDiacritics(raw).toLowerCase()
    // 3. Ukloni sve što nije slovo/broj i podeli na tokene
    const tokens = noDiacritics
      .replace(/[^a-z0-9]+/g, ' ')
      .split(/\s+/)
      .filter(Boolean)

    // 4. Filtriraj kurseve: svaki token mora da postoji u nazivu
    const filteredCourses = result.filter(d => {
      const name = d.Kurs_Naziv || ''
      // Skini dijakritike i mala slova u nazivu kursa
      const nameNorm = stripDiacritics(name).toLowerCase()
      // Proveri da li svi tokeni „ulaze“ u normalizovani naziv
      return tokens.every(token => nameNorm.includes(token))
    })

    // 5. Sortiraj rezultate po relevantnosti - prioritet za tačna poklapanja
    result = filteredCourses.sort((a, b) => {
      const nameA = stripDiacritics(a.Kurs_Naziv || '').toLowerCase()
      const nameB = stripDiacritics(b.Kurs_Naziv || '').toLowerCase()

      // Kreiraj pattern za tačno poklapanje (tokeni spojeni sa " - " ili samo razmakom)
      const exactPattern1 = tokens.join(' - ')  // npr. "engleski jezik - a1"
      const exactPattern2 = tokens.join(' ')    // npr. "engleski jezik a1"

      // Proveri da li naziv sadrži tačan pattern
      const aExactMatch1 = nameA.includes(exactPattern1)
      const bExactMatch1 = nameB.includes(exactPattern1)
      const aExactMatch2 = nameA.includes(exactPattern2)
      const bExactMatch2 = nameB.includes(exactPattern2)

      const aExactMatch = aExactMatch1 || aExactMatch2
      const bExactMatch = bExactMatch1 || bExactMatch2

      // Ako jedan ima tačno poklapanje a drugi ne, prioritet tačnom
      if (aExactMatch && !bExactMatch) return -1
      if (!aExactMatch && bExactMatch) return 1

      // Ako oba imaju ili nemaju tačno poklapanje, sortiraj po broju dodatnih reči
      const aWords = nameA.split(/\s+/).length
      const bWords = nameB.split(/\s+/).length

      // Prioritet kraćim nazivima (manje dodatnih reči)
      if (aWords !== bWords) return aWords - bWords

      // Ako je isti broj reči, sortiraj alfabetski
      return nameA.localeCompare(nameB)
    })
  }

  // Filter po oblasti
  if (filters.value.oblast) {
    result = result.filter(d => d.Oblast === filters.value.oblast)
  }

  // Filter po vrsti kursa (checkbox - može biti više)
  if (filters.value.vrsta.length > 0) {
    result = result.filter(d => filters.value.vrsta.includes(d.Vrsta))
  }

  // Filter po tipu obuke
  if (filters.value.tipObuke) {
    result = result.filter(d => d.Kurs_Obuka === filters.value.tipObuke)
  }

  // Filter po nivou
  if (filters.value.nivo) {
    // Mapiramo pun naziv nazad u originalnu skraćenicu za filtriranje
    const originalNivo = mapFullNameToNivo(filters.value.nivo)
    result = result.filter(d => d.Nivo === originalNivo)
  }

  // Filter po trajanju
  if (filters.value.trajanjeOd) {
    result = result.filter(d => d.Kurs_Trajanje_Kursa >= filters.value.trajanjeOd)
  }
  if (filters.value.trajanjeDo) {
    result = result.filter(d => d.Kurs_Trajanje_Kursa <= filters.value.trajanjeDo)
  }

  // Filter po ceni
  if (filters.value.cenaOd) {
    result = result.filter(d => d.Kcpp_Cena >= filters.value.cenaOd)
  }
  if (filters.value.cenaDo) {
    result = result.filter(d => d.Kcpp_Cena <= filters.value.cenaDo)
  }

  // Sortiranje
  return sortData(result)
})

const totalPages = computed(() => {
  if (!filteredData.value || filteredData.value.length === 0) return 1
  return Math.ceil(filteredData.value.length / itemsPerPage.value)
})

const paginatedData = computed(() => {
  if (!filteredData.value) return []

  const start = (currentPage.value - 1) * itemsPerPage.value
  return filteredData.value.slice(start, start + itemsPerPage.value)
})

// Funkcije
function nextPage() {
  if (currentPage.value < totalPages.value) currentPage.value++
}

function prevPage() {
  if (currentPage.value > 1) currentPage.value--
}

// Funkcija za mapiranje skraćenica nivoa u pune nazive
function mapNivoToFullName(nivo) {
  const nivoMapping = {
    'ODRASLI-nema nivo': 'Odrasli',
    'osnovni': 'Osnovni',
    'napredni': 'Napredni',
    'D1R': 'Dečiji - prvi razred',
    'D2R': 'Dečiji - drugi razred',
    'D3R': 'Dečiji - treći razred',
    'D4R': 'Dečiji - četvrti razred',
    'D5R': 'Dečiji - peti razred',
    'D6R': 'Dečiji - šesti razred',
    'D7R': 'Dečiji - sedmi razred',
    'D8R': 'Dečiji - osmi razred',
    'OSNŠKL': 'Osnovna škola',
    'PRPRM': 'Pripremna nastava',
    'PREDSK': 'Predškolski',
    'DECA': 'Deca',
    'SRDŠKL': 'Srednjoškolski'
  }
  return nivoMapping[nivo] || nivo
}

// Funkcija za dobijanje originalnog nivoa iz punog naziva (za filtriranje)
function mapFullNameToNivo(fullName) {
  const reverseMapping = {
    'Odrasli': 'ODRASLI-nema nivo',
    'Osnovni': 'osnovni',
    'Napredni': 'napredni',
    'Dečiji - prvi razred': 'D1R',
    'Dečiji - drugi razred': 'D2R',
    'Dečiji - treći razred': 'D3R',
    'Dečiji - četvrti razred': 'D4R',
    'Dečiji - peti razred': 'D5R',
    'Dečiji - šesti razred': 'D6R',
    'Dečiji - sedmi razred': 'D7R',
    'Dečiji - osmi razred': 'D8R',
    'Osnovna škola': 'OSNŠKL',
    'Pripremna nastava': 'PRPRM',
    'Predškolski': 'PREDSK',
    'Deca': 'DECA',
    'Srednjoškolski': 'SRDŠKL'
  }
  return reverseMapping[fullName] || fullName
}

function formatPrice(val) {
  if (!val || val === 0) return 'Na upit'
  return Number(val).toLocaleString('sr-RS') + ' RSD'
}



function sortData(data) {
  if (!data || data.length === 0) return []

  const sorted = [...data]

  switch (sortBy.value) {
    case 'naziv':
      return sorted.sort((a, b) => (a.Kurs_Naziv || '').localeCompare(b.Kurs_Naziv || ''))
    case 'naziv-desc':
      return sorted.sort((a, b) => (b.Kurs_Naziv || '').localeCompare(a.Kurs_Naziv || ''))
    case 'trajanje':
      return sorted.sort((a, b) => (a.Kurs_Trajanje_Kursa || 0) - (b.Kurs_Trajanje_Kursa || 0))
    case 'trajanje-desc':
      return sorted.sort((a, b) => (b.Kurs_Trajanje_Kursa || 0) - (a.Kurs_Trajanje_Kursa || 0))
    case 'cena':
      return sorted.sort((a, b) => (a.Kcpp_Cena || 0) - (b.Kcpp_Cena || 0))
    case 'cena-desc':
      return sorted.sort((a, b) => (b.Kcpp_Cena || 0) - (a.Kcpp_Cena || 0))
    default:
      return sorted
  }
}

function clearAllFilters() {
  filters.value = {
    search: '',
    oblast: null,
    vrsta: [],
    tipObuke: null,
    nivo: null,
    trajanjeOd: null,
    trajanjeDo: null,
    cenaOd: null,
    cenaDo: null
  }
  currentPage.value = 1
}

// Funkcija za mapiranje vrste kursa u ikonicu
function getVrstaIcon(vrsta) {
  const iconMapping = {
    'Individualni': 'bi-person',
    'Individualni - online': 'bi-wifi',
    'Poluindividualni':'bi-person-plus',
    'Poluindividualni - online':'bi-wifi',
    'Grupni': 'bi-people',
    'Grupni - online': 'bi-wifi',
    'Grupni - ubrzani': 'bi-rocket-takeoff',
    'Ubrzani': 'bi-rocket-takeoff',
    'Online': 'bi-wifi',
    'Mala grupa':'bi-grid-3x2',
    'Mikro grupa': 'bi-grid-3x2-gap'
  }
  return iconMapping[vrsta] || 'bi-book'
}

// Mobilni filter funkcije
function toggleMobileFilters() {
  showMobileFilters.value = !showMobileFilters.value
  updateBodyScroll()
}

function closeMobileFilters() {
  showMobileFilters.value = false
  updateBodyScroll()
}

function updateBodyScroll() {
  if (showMobileFilters.value) {
    document.body.classList.add('mobile-filter-open')
  } else {
    document.body.classList.remove('mobile-filter-open')
  }
}

// Watch za resetovanje stranice kada se promene filtri
watch(filters, () => {
  currentPage.value = 1
}, { deep: true })

// Watch za resetovanje nivoa kada se promeni oblast
watch(() => filters.value.oblast, () => {
  // Resetuj nivo filter kada se promeni oblast
  filters.value.nivo = null
}, { deep: true })

// Watch za resetovanje nivoa kada se promeni vrsta kursa
watch(() => filters.value.vrsta, () => {
  // Resetuj nivo filter kada se promeni vrsta kursa
  filters.value.nivo = null
}, { deep: true })

// Watch za resetovanje stranice kada se promeni broj itema po stranici
watch([selectedDesktopItemsPerPage, selectedMobileItemsPerPage], () => {
  currentPage.value = 1
})

// Cleanup kada se komponenta unmount-uje
onUnmounted(() => {
  document.body.classList.remove('mobile-filter-open')
  window.removeEventListener('resize', updateScreenSize)
})
</script>

<style scoped>
.text-orange {
  color: #f38630;
}

.btn-filter {
  background: #f38630;
  border-color: #f38630;
  color: white;
}

.btn-filter:hover {
  background: #e07b2a;
  border-color: #e07b2a;
  color: white;
}

.btn-outline-primary {
  border-color: #f38630;
  color: #f38630;
}

.btn-outline-primary:hover,
.btn-outline-primary:focus,
.btn-outline-primary.active {
  background-color: #f38630;
  border-color: #f38630;
  color: white;
}

.btn-check:checked + .btn-outline-primary {
  background-color: #f38630;
  border-color: #f38630;
  color: white;
}

.table-hover tbody tr:hover {
  background-color: rgba(243, 134, 48, 0.1);
}

.badge {
  font-size: 0.75em;
}

.bg-light-orange {
  background-color: rgba(243, 134, 48, 0.1);
}

.border-orange {
  border-color: #f38630 !important;
}

.text-muted {
  color: #6c757d !important;
}

/* Mega menu styles */
.mega-menu {
  position: static !important;
  width: 100% !important;
  left: 0 !important;
  right: 0 !important;
  border: none !important;
  border-radius: 0 !important;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
  margin-top: 0 !important;
}

.mega-menu .container-fluid {
  max-width: 1200px;
  margin: 0 auto;
}

.mega-menu-content {
  padding: 2rem 0;
}

.mega-menu-column h6 {
  color: #f38630;
  font-weight: 600;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid #f38630;
}

.mega-menu-column .dropdown-item {
  padding: 0.5rem 0;
  border: none;
  color: #333;
  transition: all 0.3s ease;
}

.mega-menu-column .dropdown-item:hover {
  background: transparent;
  color: #f38630;
  padding-left: 0.5rem;
}

/* Regular dropdown positioning */
.dropdown-menu:not(.mega-menu) {
  min-width: 250px;
}

@media (max-width: 991px) {
  .mega-menu {
    position: relative !important;
    width: auto !important;
    box-shadow: none !important;
  }

  .mega-menu-content {
    padding: 1rem 0;
  }

  .mega-menu-column {
    margin-bottom: 1.5rem;
  }
}

@media (max-width: 991px) {
  /* .table-responsive {
    font-size: 0.875rem;
  } */

  .btn {
    font-size: 1rem;
    padding: 0.375rem 0.75rem;
  }

  .form-control {
    font-size: 0.875rem;
  }
}

@media (min-width: 768px) {
  .search-input {
    min-width: 400px;
  }
}

@media (min-width: 992px) {
  .search-input {
    min-width: 550px;
  }
}
</style>